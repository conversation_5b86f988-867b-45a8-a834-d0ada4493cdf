import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {  
  try {
    const { bearerToken, cpf } = await request.json();
    console.log('🔑 Token recebido:', bearerToken ? 'Token presente' : 'Token ausente');
    console.log('📄 CPF recebido:', cpf);

    if (!bearerToken) {
      console.error('❌ Token de autenticação não fornecido');
      return NextResponse.json(
        { error: 'Token de autenticação é obrigatório' },
        { status: 400 }
      );
    }

    if (!cpf) {
      console.error('❌ CPF não fornecido');
      return NextResponse.json(
        { error: 'CPF é obrigatório' },
        { status: 400 }
      );
    }

    const SANKHYA_API_URL = process.env.SANKHYA_CRUD_LOAD_URL;
    console.log('🌐 URL da API Sankhya:', SANKHYA_API_URL);

    if (!SANKHYA_API_URL) {
      return NextResponse.json(
        { error: 'URL da API Sankhya não configurada no servidor' },
        { status: 500 }
      );
    }

    const requestBody = {
      serviceName: "CRUDServiceProvider.loadRecords",
      requestBody: {
        dataSet: {
          rootEntity: "Parceiro",
          includePresentationFields: "N",
          offsetPage: "0",
          criteria: {
            expression: {
              "$": "this.CGC_CPF = ?"
            },
            parameter: [
              {
                "$": cpf,
                "type": "S"
              }
            ]
          },
          entity: {
            fieldset: {
              list: "CODPARC,NOMEPARC,FORNECEDOR,CLIENTE,CODCID,CLIENTE,CLASSIFICMS"
            }
          }
        }
      }
    };


    const response = await axios.post(SANKHYA_API_URL, requestBody, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📥 Resposta da API Sankhya:', {
      status: response.status,
      statusText: response.statusText,
      hasData: !!response.data,
      dataKeys: response.data ? Object.keys(response.data) : []
    });

    console.log('📊 Dados completos da resposta:', JSON.stringify(response.data, null, 2));

    return NextResponse.json({
      success: true,
      data: response.data
    });

  } catch (error) {
    console.error('💥 Erro na API Route /api/sankhya/parceiro:', error);
    
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      console.error('📡 Detalhes do erro HTTP:', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        headers: axiosError.response?.headers
      });
      
      return NextResponse.json(
        {
          error: `Erro na API Sankhya: ${axiosError.response?.status} - ${axiosError.response?.data?.message || axiosError.response?.statusText}`,
          success: false,
          details: axiosError.response?.data
        },
        { status: axiosError.response?.status || 500 }
      );
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Erro desconhecido ao buscar parceiro',
        success: false
      },
      { status: 500 }
    );
  }
}
