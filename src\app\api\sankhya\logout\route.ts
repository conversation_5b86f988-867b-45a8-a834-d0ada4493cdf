import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { bearerToken } = body;

    if (!bearerToken) {
      return NextResponse.json(
        { error: 'Bearer token não fornecido para logout' },
        { status: 400 }
      );
    }

    const LOGOUT_URL = process.env.SANKHYA_LOGOUT_URL;
    const appkey = process.env.SANKHYA_APPKEY;

    if (!LOGOUT_URL || !appkey) {
      return NextResponse.json(
        { error: 'Configurações de logout não encontradas no servidor' },
        { status: 500 }
      );
    }

    // Faz a requisição de logout conforme o exemplo do cURL
    const response = await axios.post(LOGOUT_URL, {}, {
      headers: {
        'appkey': appkey,
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${bearerToken}`
      }
    });



    return NextResponse.json({
      success: true,
      message: 'Logout realizado com sucesso',
      data: response.data
    });

  } catch (error) {
    // Mesmo se o logout falhar, não queremos quebrar o fluxo principal
    return NextResponse.json({
      success: true,
      message: 'Logout tentado (pode ter falhado, mas não afeta o cadastro)',
      error: error instanceof Error ? error.message : 'Erro desconhecido no logout'
    });
  }
}
