import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
  try {
    const { bearerToken } = await request.json();

    if (!bearerToken) {
      return NextResponse.json(
        { error: 'Token de autenticação é obrigatório' },
        { status: 400 }
      );
    }

    const SANKHYA_API_URL = process.env.SANKHYA_LOADVIEW_URL;

    if (!SANKHYA_API_URL) {
      return NextResponse.json(
        { error: 'URL da API Sankhya não configurada no servidor' },
        { status: 500 }
      );
    }

    const requestBody = {
      serviceName: "CRUDServiceProvider.loadView",
      requestBody: {
        query: {
          viewName: "VW_PRODUTOS_FUNCIONARIOS",
          where: {},
          fields: {
            field: {
              "$": "CODPROD, DESCRPROD, VLRVENDA, DISPONIVEL, IMAGEM"
            }
          },
          orderBy: {
            field: {
              "$": "DESCRPROD"
            }
          }
        }
      }
    };

    const response = await axios.post(SANKHYA_API_URL, requestBody, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json'
      }
    });

    return NextResponse.json({
      success: true,
      data: response.data
    });

  } catch (error) {
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;

      return NextResponse.json(
        {
          error: `Erro na API Sankhya: ${axiosError.response?.status} - ${axiosError.response?.data?.message || axiosError.response?.statusText}`,
          success: false,
          details: axiosError.response?.data
        },
        { status: axiosError.response?.status || 500 }
      );
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Erro desconhecido ao buscar produtos',
        success: false
      },
      { status: 500 }
    );
  }
}
