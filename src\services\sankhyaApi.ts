import axios from 'axios';
import { Produto, ProdutosResponse, Parceiro, ParceiroResponse, ItemCarrinho, CompraMes, ComprasMesResponse, LimiteGastos, HistoricoPedido, HistoricoPedidosApiResponse } from '@/types/produtos';

// Interface para resposta da API route de login
interface SankhyaLoginApiResponse {
  bearerToken: string;
  success: boolean;
  error?: string;
}

// Interface para resposta do logout via API route
interface LogoutApiResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: any;
}

export const loginToSankhya = async (): Promise<string> => {
  try {
    const response = await axios.post<SankhyaLoginApiResponse>('/api/sankhya/login');

    if (response.data.success && response.data.bearerToken) {
      return response.data.bearerToken;
    } else {
      throw new Error(response.data.error || 'Token não encontrado na resposta do login');
    }
  } catch (error) {
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      if (axiosError.response?.data?.error) {
        throw new Error(axiosError.response.data.error);
      }
    }

    if (error instanceof Error) {
      throw new Error(`Falha na autenticação: ${error.message}`);
    }
    throw new Error('Falha na autenticação com a API Sankhya');
  }
};



/**
 * Realiza logout na API Sankhya através da API route
 */
export const logoutSankhya = async (bearerToken: string): Promise<void> => {
  try {
    await axios.post<LogoutApiResponse>('/api/sankhya/logout', {
      bearerToken
    });
  } catch (error) {
  }
};

// Interface para resposta da API de produtos
interface ProdutosApiResponse {
  success: boolean;
  data?: ProdutosResponse;
  error?: string;
}

// Interface para resposta da API de parceiro
interface ParceiroApiResponse {
  success: boolean;
  data?: ParceiroResponse;
  error?: string;
}

// Interface para resposta da API de incluir nota
interface IncluirNotaApiResponse {
  success: boolean;
  data?: any;
  error?: string;
}

// Interface para resposta da API de confirmar nota
interface ConfirmarNotaApiResponse {
  success: boolean;
  data?: any;
  error?: string;
}

// Interface para resposta da API de compras do mês
interface ComprasMesApiResponse {
  success: boolean;
  data?: ComprasMesResponse;
  error?: string;
}

// Interface para resposta da API de atualizar tabela do parceiro
interface AtualizarTabelaParceiroApiResponse {
  success: boolean;
  data?: any;
  error?: string;
}

/**
 * Atualiza a tabela do parceiro para 1500
 */
export const atualizarTabelaParceiro = async (bearerToken: string, codparc: number): Promise<void> => {
  try {
    const response = await axios.post<AtualizarTabelaParceiroApiResponse>('/api/sankhya/atualizar-tabela-parceiro', {
      bearerToken,
      codparc
    });

    if (!response.data.success) {
      throw new Error(response.data.error || 'Erro ao atualizar tabela do parceiro');
    }

    console.log('✅ Tabela do parceiro atualizada com sucesso para CODTAB = 1500');
  } catch (error) {
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      if (axiosError.response?.data?.error) {
        throw new Error(axiosError.response.data.error);
      }
    }

    if (error instanceof Error) {
      throw new Error(`Falha ao atualizar tabela do parceiro: ${error.message}`);
    }
    throw new Error('Falha ao atualizar tabela do parceiro');
  }
};

/**
 * Busca parceiro pelo CPF
 */
export const buscarParceiroPorCPF = async (bearerToken: string, cpf: string): Promise<Parceiro | null> => {
  try {
    const response = await axios.post<ParceiroApiResponse>('/api/sankhya/parceiro', {
      bearerToken,
      cpf
    });

    if (!response.data.success) {
      throw new Error(response.data.error || 'Erro ao buscar parceiro');
    }

    const parceiroData = response.data.data;

    if (parceiroData?.responseBody?.entities?.entity) {
      const parceiroRaw = parceiroData.responseBody.entities.entity;

      if (parceiroRaw.f0 && parceiroRaw.f1) {
        const parceiro: Parceiro = {
          CODPARC: parseInt(parceiroRaw.f0.$),
          NOMEPARC: parceiroRaw.f1.$,
          FORNECEDOR: parceiroRaw.f2.$,
          CLIENTE: parceiroRaw.f3.$,
          CODCID: parseInt(parceiroRaw.f4.$),
          CLASSIFICMS: parceiroRaw.f5.$,
          CODTAB: parseInt(parceiroRaw.f6.$)
        };

        return parceiro;
      }
    }

    return null;
  } catch (error) {
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      if (axiosError.response?.data?.error) {
        throw new Error(axiosError.response.data.error);
      }
    }

    if (error instanceof Error) {
      throw new Error(`Falha ao buscar parceiro: ${error.message}`);
    }
    throw new Error('Falha ao buscar parceiro');
  }
};

/**
 * Busca produtos da view VW_PRODUTOS_FUNCIONARIOS
 */
export const buscarProdutos = async (bearerToken: string): Promise<Produto[]> => {
  console.log('🔍 Iniciando busca de produtos...');
  console.log('🔑 Bearer token recebido:', bearerToken ? 'Token presente' : 'Token ausente');

  try {
    console.log('📡 Fazendo requisição para /api/sankhya/produtos');

    const response = await axios.post<ProdutosApiResponse>('/api/sankhya/produtos', {
      bearerToken
    });

    console.log('📥 Resposta recebida:', {
      status: response.status,
      success: response.data.success,
      hasData: !!response.data.data
    });

    if (!response.data.success) {
      console.error('❌ Erro na resposta da API:', response.data.error);
      throw new Error(response.data.error || 'Erro ao buscar produtos');
    }

    // Extrair os produtos da resposta da API Sankhya
    const produtosData = response.data.data;
    console.log('📊 Estrutura dos dados recebidos:', {
      hasResponseBody: !!produtosData?.responseBody,
      hasRecords: !!produtosData?.responseBody?.records,
      hasRecord: !!produtosData?.responseBody?.records?.record,
      recordType: Array.isArray(produtosData?.responseBody?.records?.record) ? 'array' : typeof produtosData?.responseBody?.records?.record
    });

    if (produtosData?.responseBody?.records?.record) {
      const produtosRaw = Array.isArray(produtosData.responseBody.records.record)
        ? produtosData.responseBody.records.record
        : [produtosData.responseBody.records.record];

      // Converter os dados da estrutura da API para o formato esperado
      const produtos: Produto[] = produtosRaw.map(produto => ({
        CODPROD: parseInt(produto.CODPROD.$),
        DESCRPROD: produto.DESCRPROD.$,
        VLRVENDA: parseFloat(produto.VLRVENDA.$),
        DISPONIVEL: parseFloat(produto.DISPONIVEL.$),
        IMAGEM: produto.IMAGEM?.$
      }));

      // Ordenar produtos alfabeticamente por descrição
      produtos.sort((a, b) => a.DESCRPROD.localeCompare(b.DESCRPROD, 'pt-BR', {
        sensitivity: 'base'
      }));

      console.log('✅ Produtos encontrados:', produtos.length);
      console.log('📋 Primeiro produto (exemplo):', produtos[0]);

      return produtos;
    }

    console.log('⚠️ Nenhum produto encontrado na estrutura de dados');
    return [];
  } catch (error) {
    console.error('💥 Erro ao buscar produtos:', error);

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      console.error('📡 Detalhes do erro HTTP:', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data
      });

      if (axiosError.response?.data?.error) {
        throw new Error(axiosError.response.data.error);
      }
    }

    if (error instanceof Error) {
      throw new Error(`Falha ao buscar produtos: ${error.message}`);
    }
    throw new Error('Falha ao buscar produtos');
  }
};

/**
 * Buscar compras do mês atual para calcular limite de gastos (usando token existente)
 */
export const buscarComprasDoMesComToken = async (bearerToken: string, codparc: number): Promise<LimiteGastos> => {
  console.log('🔍 Iniciando busca de compras do mês...');
  console.log('🔑 Bearer token recebido:', bearerToken ? 'Token presente' : 'Token ausente');
  console.log('👤 CODPARC:', codparc);

  try {
    console.log('📡 Fazendo requisição para /api/sankhya/compras-mes');

    const response = await axios.post<ComprasMesApiResponse>('/api/sankhya/compras-mes', {
      bearerToken,
      codparc
    });

    console.log('📥 Resposta recebida:', {
      status: response.status,
      success: response.data.success,
      hasData: !!response.data.data
    });

    if (!response.data.success) {
      console.error('❌ Erro na resposta da API:', response.data.error);
      throw new Error(response.data.error || 'Erro ao buscar compras do mês');
    }

    const apiData = response.data.data;
    if (!apiData || !apiData.responseBody || !apiData.responseBody.entities) {
      console.log('📊 Nenhuma compra encontrada no mês atual');
      return {
        limiteTotal: 220.00,
        gastoAtual: 0.00,
        saldoDisponivel: 220.00,
        comprasDoMes: []
      };
    }

    // Processar entidades retornadas
    const entities = apiData.responseBody.entities;
    const comprasDoMes: CompraMes[] = [];
    let gastoTotal = 0;

    if (entities.entity) {
      // Verificar se é um array ou objeto único
      const entitiesArray = Array.isArray(entities.entity) ? entities.entity : [entities.entity];

      for (const entity of entitiesArray) {
        const compra: CompraMes = {
          NUNOTA: parseInt(entity.f0.$),
          CODEMP: parseInt(entity.f1.$),
          CODPARC: parseInt(entity.f2.$),
          DTNEG: entity.f3.$,
          VLRNOTA: parseFloat(entity.f4.$)
        };

        comprasDoMes.push(compra);
        gastoTotal += compra.VLRNOTA;

        console.log(`📄 Compra encontrada: NUNOTA ${compra.NUNOTA} - R$ ${compra.VLRNOTA.toFixed(2)}`);
      }
    }

    const limiteTotal = 220.00;
    const saldoDisponivel = Math.max(0, limiteTotal - gastoTotal);

    console.log(`💰 Resumo financeiro:`);
    console.log(`   Limite total: R$ ${limiteTotal.toFixed(2)}`);
    console.log(`   Gasto atual: R$ ${gastoTotal.toFixed(2)}`);
    console.log(`   Saldo disponível: R$ ${saldoDisponivel.toFixed(2)}`);
    console.log(`   Compras no mês: ${comprasDoMes.length}`);

    return {
      limiteTotal,
      gastoAtual: gastoTotal,
      saldoDisponivel,
      comprasDoMes
    };

  } catch (error) {
    console.error('💥 Erro ao buscar compras do mês:', error);

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      console.error('📡 Detalhes do erro HTTP:', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data
      });

      if (axiosError.response?.data?.error) {
        throw new Error(axiosError.response.data.error);
      }
    }

    if (error instanceof Error) {
      throw new Error(`Falha ao buscar compras do mês: ${error.message}`);
    }
    throw new Error('Falha ao buscar compras do mês');
  }
};

/**
 * Buscar compras do mês atual para calcular limite de gastos (fluxo completo com login/logout)
 */
export const buscarComprasDoMes = async (codparc: number): Promise<LimiteGastos> => {
  console.log('🚀 Iniciando fluxo completo de busca de compras do mês...');
  let token: string | null = null;

  try {
    console.log('1️⃣ Etapa 1: Login');
    token = await loginToSankhya();

    console.log('2️⃣ Etapa 2: Buscar compras do mês');
    const limiteGastos = await buscarComprasDoMesComToken(token, codparc);

    console.log('3️⃣ Etapa 3: Logout');
    await logoutSankhya(token);

    console.log('✅ Fluxo completo finalizado com sucesso');
    return limiteGastos;
  } catch (error) {
    console.error('💥 Erro no fluxo completo de busca de compras do mês:', error);

    if (token) {
      console.log('🔄 Tentando fazer logout após erro...');
      try {
        await logoutSankhya(token);
        console.log('✅ Logout realizado após erro');
      } catch (logoutError) {
        console.error('❌ Erro no logout após falha:', logoutError);
      }
    }
    throw error;
  }
};

/**
 * Confirmar nota na API Sankhya
 */
export const confirmarNota = async (bearerToken: string, nunota: number): Promise<any> => {
  console.log('🔍 Iniciando confirmação de nota...');
  console.log('🔑 Bearer token recebido:', bearerToken ? 'Token presente' : 'Token ausente');
  console.log('📄 NUNOTA:', nunota);

  try {
    console.log('📡 Fazendo requisição para /api/sankhya/confirmar-nota');

    const response = await axios.post<ConfirmarNotaApiResponse>('/api/sankhya/confirmar-nota', {
      bearerToken,
      nunota
    });

    console.log('📥 Resposta recebida:', {
      status: response.status,
      success: response.data.success,
      hasData: !!response.data.data
    });

    if (!response.data.success) {
      console.error('❌ Erro na resposta da API:', response.data.error);
      throw new Error(response.data.error || 'Erro ao confirmar nota');
    }

    console.log('✅ Nota confirmada com sucesso');
    return response.data.data;
  } catch (error) {
    console.error('💥 Erro ao confirmar nota:', error);

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      console.error('📡 Detalhes do erro HTTP:', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data
      });

      if (axiosError.response?.data?.error) {
        throw new Error(axiosError.response.data.error);
      }
    }

    if (error instanceof Error) {
      throw new Error(`Falha ao confirmar nota: ${error.message}`);
    }
    throw new Error('Falha ao confirmar nota');
  }
};

/**
 * Incluir nota/pedido na API Sankhya
 */
export const incluirNota = async (bearerToken: string, codparc: number, itens: ItemCarrinho[]): Promise<any> => {
  console.log('🔍 Iniciando inclusão de nota...');
  console.log('🔑 Bearer token recebido:', bearerToken ? 'Token presente' : 'Token ausente');
  console.log('👤 CODPARC:', codparc);
  console.log('📦 Itens:', itens.length);

  try {
    console.log('📡 Fazendo requisição para /api/sankhya/incluir-nota');

    // Preparar itens para envio
    const itensFormatados = itens.map(item => ({
      codprod: item.produto.CODPROD,
      quantidade: item.quantidade
    }));

    const response = await axios.post<IncluirNotaApiResponse>('/api/sankhya/incluir-nota', {
      bearerToken,
      codparc,
      itens: itensFormatados
    });

    console.log('📥 Resposta recebida:', {
      status: response.status,
      success: response.data.success,
      hasData: !!response.data.data
    });

    if (!response.data.success) {
      console.error('❌ Erro na resposta da API:', response.data.error);
      throw new Error(response.data.error || 'Erro ao incluir nota');
    }

    console.log('✅ Nota incluída com sucesso');
    return response.data.data;
  } catch (error) {
    console.error('💥 Erro ao incluir nota:', error);

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      console.error('📡 Detalhes do erro HTTP:', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data
      });

      if (axiosError.response?.data?.error) {
        throw new Error(axiosError.response.data.error);
      }
    }

    if (error instanceof Error) {
      throw new Error(`Falha ao incluir nota: ${error.message}`);
    }
    throw new Error('Falha ao incluir nota');
  }
};

/**
 * Fluxo completo: login + buscar parceiro por CPF + buscar limite de gastos + logout
 */
export const buscarParceiroCompleto = async (cpf: string): Promise<{ parceiro: Parceiro; limiteGastos: LimiteGastos } | null> => {
  console.log('🚀 Iniciando fluxo completo de busca de parceiro...');
  let bearerToken: string | null = null;

  try {
    console.log('1️⃣ Etapa 1: Login');
    bearerToken = await loginToSankhya();

    console.log('2️⃣ Etapa 2: Buscar parceiro por CPF');
    const parceiro = await buscarParceiroPorCPF(bearerToken, cpf);

    if (!parceiro) {
      console.log('❌ Parceiro não encontrado');
      await logoutSankhya(bearerToken);
      return null;
    }

    console.log('🔍 Verificando CODTAB do parceiro:', parceiro.CODTAB);

    // Verificar se CODTAB é diferente de 1500 e atualizar se necessário
    if (parceiro.CODTAB !== 1500) {
      console.log('⚠️ CODTAB não é 1500, atualizando para 1500...');
      try {
        await atualizarTabelaParceiro(bearerToken, parceiro.CODPARC);
        // Atualizar o objeto parceiro localmente
        parceiro.CODTAB = 1500;
        console.log('✅ CODTAB atualizado com sucesso para 1500');
      } catch (updateError) {
        console.error('❌ Erro ao atualizar CODTAB:', updateError);
        // Continua o fluxo mesmo se a atualização falhar
      }
    } else {
      console.log('✅ CODTAB já é 1500, seguindo fluxo normal');
    }

    console.log('3️⃣ Etapa 3: Buscar limite de gastos');
    const limiteGastos = await buscarComprasDoMesComToken(bearerToken, parceiro.CODPARC);

    console.log('4️⃣ Etapa 4: Logout');
    await logoutSankhya(bearerToken);

    console.log('✅ Fluxo completo finalizado com sucesso. Parceiro encontrado:', !!parceiro);
    return { parceiro, limiteGastos };
  } catch (error) {
    console.error('💥 Erro no fluxo completo de busca de parceiro:', error);

    if (bearerToken) {
      console.log('🔄 Tentando fazer logout após erro...');
      try {
        await logoutSankhya(bearerToken);
        console.log('✅ Logout realizado após erro');
      } catch (logoutError) {
        console.error('❌ Erro no logout após falha:', logoutError);
      }
    }
    throw error;
  }
};

/**
 * Fluxo completo: login + buscar produtos + logout
 */
export const buscarProdutosCompleto = async (): Promise<Produto[]> => {
  console.log('🚀 Iniciando fluxo completo de busca de produtos...');
  let bearerToken: string | null = null;

  try {
    console.log('1️⃣ Etapa 1: Login');
    bearerToken = await loginToSankhya();

    console.log('2️⃣ Etapa 2: Buscar produtos');
    const produtos = await buscarProdutos(bearerToken);

    console.log('3️⃣ Etapa 3: Logout');
    await logoutSankhya(bearerToken);

    console.log('✅ Fluxo completo finalizado com sucesso. Produtos encontrados:', produtos.length);
    return produtos;
  } catch (error) {
    console.error('💥 Erro no fluxo completo:', error);

    if (bearerToken) {
      console.log('🔄 Tentando fazer logout após erro...');
      try {
        await logoutSankhya(bearerToken);
        console.log('✅ Logout realizado após erro');
      } catch (logoutError) {
        console.error('❌ Erro no logout após falha:', logoutError);
      }
    }
    throw error;
  }
};

/**
 * Buscar histórico de pedidos do parceiro
 */
export const buscarHistoricoPedidos = async (codparc: number): Promise<HistoricoPedido[]> => {
  console.log('🔍 Iniciando busca de histórico de pedidos...');
  console.log('👤 CODPARC:', codparc);

  try {
    console.log('📡 Fazendo requisição para /api/sankhya/historico-pedidos');
    const response = await axios.post<HistoricoPedidosApiResponse>('/api/sankhya/historico-pedidos', {
      codparc
    });

    console.log('📥 Resposta recebida:', {
      status: response.status,
      success: response.data.success,
      pedidosCount: response.data.pedidos?.length || 0
    });

    if (response.data.success && response.data.pedidos) {
      console.log('✅ Histórico de pedidos obtido com sucesso');
      return response.data.pedidos;
    } else {
      console.error('❌ Erro na resposta da API:', response.data.error);
      throw new Error(response.data.error || 'Erro ao buscar histórico de pedidos');
    }
  } catch (error) {
    console.error('💥 Erro ao buscar histórico de pedidos:', error);

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      console.error('📡 Detalhes do erro HTTP:', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data
      });

      if (axiosError.response?.data?.error) {
        throw new Error(axiosError.response.data.error);
      }
    }

    if (error instanceof Error) {
      throw new Error(`Erro ao buscar histórico de pedidos: ${error.message}`);
    }
    throw new Error('Erro desconhecido ao buscar histórico de pedidos');
  }
};

/**
 * Fluxo completo: login + incluir nota + confirmar nota + logout
 */
export const incluirNotaCompleto = async (codparc: number, itens: ItemCarrinho[]): Promise<any> => {
  console.log('🚀 Iniciando fluxo completo de inclusão e confirmação de nota...');
  let bearerToken: string | null = null;

  try {
    console.log('1️⃣ Etapa 1: Login');
    bearerToken = await loginToSankhya();

    console.log('2️⃣ Etapa 2: Incluir nota');
    const resultadoInclusao = await incluirNota(bearerToken, codparc, itens);

    // Extrair NUNOTA da resposta
    let nunota: number | null = null;
    if (resultadoInclusao && resultadoInclusao.responseBody && resultadoInclusao.responseBody.pk) {
      nunota = parseInt(resultadoInclusao.responseBody.pk.NUNOTA.$);
      console.log('📄 NUNOTA obtido da inclusão:', nunota);
    }

    if (!nunota) {
      console.error('❌ NUNOTA não encontrado na resposta da inclusão');
      throw new Error('NUNOTA não encontrado na resposta da inclusão da nota');
    }

    console.log('3️⃣ Etapa 3: Confirmar nota');
    const resultadoConfirmacao = await confirmarNota(bearerToken, nunota);

    // Retornar dados incluindo bearerToken antes do logout
    const resultado = {
      inclusao: resultadoInclusao,
      confirmacao: resultadoConfirmacao,
      nunota: nunota,
      bearerToken: bearerToken
    };

    console.log('4️⃣ Etapa 4: Logout');
    await logoutSankhya(bearerToken);

    console.log('✅ Fluxo completo finalizado com sucesso');
    return resultado;
  } catch (error) {
    console.error('💥 Erro no fluxo completo de inclusão e confirmação de nota:', error);

    if (bearerToken) {
      console.log('🔄 Tentando fazer logout após erro...');
      try {
        await logoutSankhya(bearerToken);
        console.log('✅ Logout realizado após erro');
      } catch (logoutError) {
        console.error('❌ Erro no logout após falha:', logoutError);
      }
    }
    throw error;
  }
};
