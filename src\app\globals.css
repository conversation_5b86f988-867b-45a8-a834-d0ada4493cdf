@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Ocultar indicador de desenvolvimento do Next.js */
#__next-build-watcher,
[data-nextjs-toast-errors],
[data-nextjs-toast-errors-parent],
.__next-dev-overlay-backdrop,
.__next-dev-overlay,
[data-nextjs-dialog-overlay],
[data-nextjs-dialog],
[data-nextjs-portal] {
  display: none !important;
}

/* Ocultar qualquer elemento com "N" no canto inferior esquerdo */
div[style*="position: fixed"][style*="bottom"][style*="left"] {
  display: none !important;
}
