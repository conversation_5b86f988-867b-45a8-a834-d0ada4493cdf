@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Ocultar indicador de desenvolvimento do Next.js */
#__next-build-watcher,
[data-nextjs-toast-errors],
[data-nextjs-toast-errors-parent],
.__next-dev-overlay-backdrop,
.__next-dev-overlay,
[data-nextjs-dialog-overlay],
[data-nextjs-dialog],
[data-nextjs-portal],
[data-nextjs-toast="true"],
.nextjs-toast,
[data-next-badge-root],
[data-nextjs-dev-tools-button],
[data-next-badge],
[data-next-mark],
svg[data-next-mark-loading],
div[style*="position: fixed"][style*="bottom: 20px"][style*="left: 20px"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Regra mais específica para o toast do Next.js */
div[data-nextjs-toast="true"] * {
  display: none !important;
}

/* Ocultar qualquer SVG que contenha gradientes do Next.js */
svg:has(defs linearGradient[id*="next_logo"]) {
  display: none !important;
}

/* Força ocultar tudo relacionado ao Next.js dev tools */
* {
  &:has(svg[viewBox="0 0 40 40"]) {
    display: none !important;
  }
}

/* Última tentativa - ocultar por posição específica */
body > div[style*="z-index: 2147483647"] {
  display: none !important;
}
