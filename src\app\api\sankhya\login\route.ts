import { NextResponse } from 'next/server';
import axios from 'axios';

interface SankhyaLoginResponse {
  bearerToken: string;
}

export async function POST() {
  try {
    const loginCredentials = {
      usuario: process.env.SANKHYA_USUARIO,
      senha: process.env.SANKHYA_SENHA,
      token: process.env.SANKHYA_TOKEN,
      appkey: process.env.SANKHYA_APPKEY
    };

    const SANKHYA_LOGIN_URL = process.env.SANKHYA_LOGIN_URL;

    if (!SANKHYA_LOGIN_URL || !loginCredentials.usuario || !loginCredentials.senha ||
        !loginCredentials.token || !loginCredentials.appkey) {
      return NextResponse.json(
        { error: 'Configurações da API Sankhya não encontradas no servidor' },
        { status: 500 }
      );
    }

    const response = await axios.post<SankhyaLoginResponse>(SANKHYA_LOGIN_URL, {}, {
      headers: {
        'username': loginCredentials.usuario,
        'password': loginCredentials.senha,
        'token': loginCredentials.token,
        'appkey': loginCredentials.appkey,
        'Content-Type': 'application/json'
      }
    });

    return NextResponse.json({
      bearerToken: response.data.bearerToken,
      success: true
    });

  } catch (error) {
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      return NextResponse.json(
        {
          error: `Erro na API Sankhya: ${axiosError.response?.status} - ${axiosError.response?.data?.message || axiosError.response?.statusText}`,
          success: false
        },
        { status: axiosError.response?.status || 500 }
      );
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Erro desconhecido no login',
        success: false
      },
      { status: 500 }
    );
  }
}
