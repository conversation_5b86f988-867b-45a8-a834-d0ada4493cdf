'use client';

import { useState, useEffect } from 'react';
import { Produto, FeedbackMessage, Parceiro, ItemCarrinho, LimiteGastos, HistoricoPedido } from '@/types/produtos';
import { buscarProdutosCompleto, buscarParceiroCompleto, incluirNotaCompleto, buscarHistoricoPedidos } from '@/services/sankhyaApi';
import Image from 'next/image';

export default function ListaProdutos() {
  const [produtos, setProdutos] = useState<Produto[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<FeedbackMessage | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [cpf, setCpf] = useState('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [parceiro, setParceiro] = useState<Parceiro | null>(null);
  const [limiteGastos, setLimiteGastos] = useState<LimiteGastos | null>(null);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [carrinho, setCarrinho] = useState<ItemCarrinho[]>([]);
  const [mostrarCarrinho, setMostrarCarrinho] = useState(false);
  const [isFinalizandoPedido, setIsFinalizandoPedido] = useState(false);
  const [historicoPedidos, setHistoricoPedidos] = useState<HistoricoPedido[]>([]);
  const [mostrarHistorico, setMostrarHistorico] = useState(false);
  const [isCarregandoHistorico, setIsCarregandoHistorico] = useState(false);
  const [imagemModal, setImagemModal] = useState<{produto: Produto, url: string} | null>(null);

  const [mostrarBotaoTopo, setMostrarBotaoTopo] = useState(false);
  const [mostrarNotificacaoCarrinho, setMostrarNotificacaoCarrinho] = useState(false);
  const [mostrarPopupSucesso, setMostrarPopupSucesso] = useState(false);

  // Detectar scroll para mostrar/ocultar botão de voltar ao topo
  useEffect(() => {
    const handleScroll = () => {
      setMostrarBotaoTopo(window.scrollY > 150);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Bloquear scroll quando modais estão abertos
  useEffect(() => {
    if (mostrarCarrinho || mostrarHistorico || imagemModal || mostrarPopupSucesso) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup quando componente desmonta
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [mostrarCarrinho, mostrarHistorico, imagemModal, mostrarPopupSucesso]);

  // Função para voltar ao topo
  const voltarAoTopo = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const formatarCPF = (valor: string) => {
    // Remove todos os caracteres não numéricos
    const apenasNumeros = valor.replace(/\D/g, '');

    // Limita a 11 dígitos
    const limitado = apenasNumeros.substring(0, 11);

    // Aplica a formatação
    if (limitado.length <= 3) {
      return limitado;
    } else if (limitado.length <= 6) {
      return limitado.replace(/(\d{3})(\d+)/, '$1.$2');
    } else if (limitado.length <= 9) {
      return limitado.replace(/(\d{3})(\d{3})(\d+)/, '$1.$2.$3');
    } else {
      return limitado.replace(/(\d{3})(\d{3})(\d{3})(\d+)/, '$1.$2.$3-$4');
    }
  };

  const handleCPFChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const valorFormatado = formatarCPF(e.target.value);
    setCpf(valorFormatado);
  };

  const autenticarUsuario = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsAuthenticating(true);
    setMessage(null);

    const cpfLimpo = cpf.replace(/\D/g, '');

    if (cpfLimpo.length !== 11) {
      setMessage({
        type: 'error',
        text: `CPF deve conter exatamente 11 dígitos. Você digitou ${cpfLimpo.length} dígitos.`
      });
      setIsAuthenticating(false);
      return;
    }

    try {
      const resultado = await buscarParceiroCompleto(cpfLimpo);

      if (resultado) {
        setParceiro(resultado.parceiro);
        setLimiteGastos(resultado.limiteGastos);
        setIsLoggedIn(true);
        setMessage({
          type: 'success',
          text: `Bem-vindo, ${resultado.parceiro.NOMEPARC}! Saldo disponível: R$ ${resultado.limiteGastos.saldoDisponivel.toFixed(2)}`
        });
        await carregarProdutos();
      } else {
        setMessage({
          type: 'error',
          text: 'CPF não encontrado. Verifique se você está cadastrado no sistema.'
        });
      }
    } catch (error) {
      setMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'Erro ao autenticar usuário'
      });
    } finally {
      setIsAuthenticating(false);
    }
  };

  const carregarProdutos = async () => {
    setIsLoading(true);
    setMessage(null);

    try {
      const produtosData = await buscarProdutosCompleto();
      setProdutos(produtosData);
    } catch (error) {
      setMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'Erro ao carregar produtos'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setIsLoggedIn(false);
    setParceiro(null);
    setLimiteGastos(null);
    setProdutos([]);
    setCpf('');
    setSearchTerm('');
    setMessage(null);
    setCarrinho([]);
    setMostrarCarrinho(false);
  };

  const adicionarAoCarrinho = (produto: Produto) => {
    setCarrinho(carrinhoAtual => {
      const itemExistente = carrinhoAtual.find(item => item.produto.CODPROD === produto.CODPROD);

      if (itemExistente) {
        // Se já existe, aumenta a quantidade
        return carrinhoAtual.map(item =>
          item.produto.CODPROD === produto.CODPROD
            ? { ...item, quantidade: item.quantidade + 1 }
            : item
        );
      } else {
        // Se não existe, adiciona com quantidade 1
        return [...carrinhoAtual, { produto, quantidade: 1 }];
      }
    });

    setMessage({
      type: 'success',
      text: `${produto.DESCRPROD} adicionado ao carrinho!`
    });

    // Mostrar animação de notificação por 3 segundos
    setMostrarNotificacaoCarrinho(true);
    setTimeout(() => setMostrarNotificacaoCarrinho(false), 3000);

    // Limpar mensagem após 2 segundos
    setTimeout(() => setMessage(null), 2000);
  };

  const removerDoCarrinho = (codprod: number) => {
    setCarrinho(carrinhoAtual => carrinhoAtual.filter(item => item.produto.CODPROD !== codprod));
  };

  const atualizarQuantidade = (codprod: number, novaQuantidade: number) => {
    if (novaQuantidade <= 0) {
      removerDoCarrinho(codprod);
      return;
    }

    setCarrinho(carrinhoAtual =>
      carrinhoAtual.map(item =>
        item.produto.CODPROD === codprod
          ? { ...item, quantidade: novaQuantidade }
          : item
      )
    );
  };

  const calcularTotalCarrinho = () => {
    return carrinho.reduce((total, item) => total + (item.produto.VLRVENDA * item.quantidade), 0);
  };

  const carregarHistoricoPedidos = async () => {
    if (!parceiro) {
      setMessage({
        type: 'error',
        text: 'Usuário não autenticado'
      });
      return;
    }

    setIsCarregandoHistorico(true);
    setMessage(null);

    try {
      const historico = await buscarHistoricoPedidos(parceiro.CODPARC);
      setHistoricoPedidos(historico);
      setMostrarHistorico(true);

      setMessage({
        type: 'success',
        text: `${historico.length} pedido(s) encontrado(s)`
      });
      setTimeout(() => setMessage(null), 2000);
    } catch (error) {
      setMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'Erro ao carregar histórico de pedidos'
      });
    } finally {
      setIsCarregandoHistorico(false);
    }
  };

  const atualizarSaldoDisponivel = async (valorGasto?: number) => {
    if (!parceiro || !limiteGastos) return;

    // Se temos o valor gasto, atualiza imediatamente no frontend
    if (valorGasto) {
      const novoSaldo = {
        ...limiteGastos,
        gastoAtual: limiteGastos.gastoAtual + valorGasto,
        saldoDisponivel: limiteGastos.saldoDisponivel - valorGasto
      };
      setLimiteGastos(novoSaldo);
    }
  };

  const refazerPedido = async (pedido: HistoricoPedido) => {
    setCarrinho([]);

    const novosItens: ItemCarrinho[] = [];

    for (const item of pedido.itens) {
      const produtoEncontrado = produtos.find(p => p.CODPROD === item.codprod);

      if (produtoEncontrado) {
        novosItens.push({
          produto: produtoEncontrado,
          quantidade: 1
        });
      }
    }

    setCarrinho(novosItens);
    setMostrarHistorico(false);
    setMostrarCarrinho(true);

    setMessage({
      type: 'success',
      text: `${novosItens.length} produto(s) adicionado(s) ao carrinho`
    });
  };

  const finalizarPedido = async () => {
    if (!parceiro || !limiteGastos || carrinho.length === 0) {
      setMessage({
        type: 'error',
        text: 'Carrinho vazio ou usuário não autenticado'
      });
      return;
    }

    // Verificar se o valor do carrinho não excede o limite disponível
    const totalCarrinho = calcularTotalCarrinho();
    if (totalCarrinho > limiteGastos.saldoDisponivel) {
      setMessage({
        type: 'error',
        text: `Valor do carrinho (R$ ${totalCarrinho.toFixed(2)}) excede o saldo disponível (R$ ${limiteGastos.saldoDisponivel.toFixed(2)})`
      });
      return;
    }

    setIsFinalizandoPedido(true);
    setMessage(null);

    try {
      await incluirNotaCompleto(parceiro.CODPARC, carrinho);

      const valorGasto = totalCarrinho;

      // Mostrar popup de sucesso
      setMostrarPopupSucesso(true);

      setCarrinho([]);
      setMostrarCarrinho(false);

      // Atualizar o saldo disponível automaticamente (com valor gasto para atualização imediata)
      await atualizarSaldoDisponivel(valorGasto);

      // Recarregar a página após 3 segundos para fazer logout automático
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    } catch (error) {
      setMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'Erro ao finalizar pedido'
      });
    } finally {
      setIsFinalizandoPedido(false);
    }
  };

  const produtosFiltrados = produtos.filter(produto =>
    produto.DESCRPROD.toLowerCase().includes(searchTerm.toLowerCase()) ||
    produto.CODPROD.toString().includes(searchTerm)
  );

  const formatarPreco = (preco: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(preco);
  };

  const formatarEstoque = (quantidade: number) => {
    return quantidade > 0 ? 'DISPONÍVEL' : 'INDISPONÍVEL';
  };



  // Componente de imagem usando endpoint direto do Sankhya
  const ImagemProduto = ({ produto, className = "w-full h-full object-cover" }: { produto: Produto, className?: string }) => {
    const [erro, setErro] = useState(false);

    // URL direta para imagem do produto no Sankhya
    const imagemUrl = `http://cepera.fwc.cloud:8180/mge/Produto@IMAGEM@CODPROD=${produto.CODPROD}.dbimage`;

    const abrirModal = () => {
      if (!erro) {
        setImagemModal({ produto, url: imagemUrl });
      }
    };

    if (erro) {
      return (
        <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-400 text-xs text-center">
          📦<br/>
          <span className="text-[10px]">{produto.CODPROD}</span>
        </div>
      );
    }

    return (
      <img
        src={imagemUrl}
        alt={produto.DESCRPROD}
        className={`${className} cursor-pointer hover:opacity-80 transition-opacity`}
        onError={() => setErro(true)}
        onLoad={() => setErro(false)}
        onClick={abrirModal}
        title="Clique para ampliar"
      />
    );
  };



  // Tela de login
  if (!isLoggedIn) {
    return (
      <div
        className="min-h-screen flex items-center justify-center p-4"
        style={{
          backgroundImage: 'url(/bg_cepera.png)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md">
          <div className="flex justify-center mb-6">
            <Image
              src="/logo.png"
              alt="Cepêra Logo"
              width={120}
              height={57}
              className="object-contain"
            />
          </div>

          <h1 className="text-2xl font-bold text-center mb-6" style={{ color: '#0B4C02' }}>
            Pedido Funcionário
          </h1>

          <p className="text-center mb-6" style={{ color: '#4B5563' }}>
            Digite seu CPF para acessar os produtos!
          </p>

          {message && (
            <div className={`mb-4 p-4 rounded-lg ${message.type === 'success'
                ? 'bg-green-50 border border-green-200'
                : 'bg-red-50 text-red-800 border border-red-200'
              }`} style={message.type === 'success' ? { color: '#0B4C02' } : {}}>
              {message.text}
            </div>
          )}

          <form onSubmit={autenticarUsuario} className="space-y-4">
            <div className="relative">
              <input
                type="tel"
                inputMode="numeric"
                pattern="[0-9]{3}\.[0-9]{3}\.[0-9]{3}-[0-9]{2}"
                value={cpf}
                onChange={handleCPFChange}
                placeholder="000.000.000-00"
                maxLength={14}
                className="w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 text-center text-lg"
                style={{
                  borderColor: '#0B4C02',
                  color: '#000000',
                  WebkitTextFillColor: '#000000'
                }}
                disabled={isAuthenticating}
              />
              <label className="absolute -top-2 left-3 bg-white px-1 text-sm font-medium" style={{ color: '#374151' }}>
                CPF
              </label>
            </div>

            <button
              type="submit"
              disabled={isAuthenticating || cpf.replace(/\D/g, '').length !== 11}
              className="w-full text-white py-3 px-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              style={{ backgroundColor: '#0B4C02' }}
              onMouseEnter={(e) => !isAuthenticating && (e.currentTarget.style.backgroundColor = '#0A4001')}
              onMouseLeave={(e) => !isAuthenticating && (e.currentTarget.style.backgroundColor = '#0B4C02')}
            >
              {isAuthenticating ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Verificando...
                </div>
              ) : (
                'Acessar Produtos'
              )}
            </button>
          </form>
        </div>
      </div>
    );
  }

  // Tela de produtos
  return (
    <div
      className="min-h-screen p-4"
      style={{
        backgroundImage: 'url(/bg_cepera.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-2xl shadow-2xl p-6 mb-6">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mb-4">
            <div className="flex items-center gap-4">
              <Image
                src="/logo.png"
                alt="Cepêra Logo"
                width={120}
                height={57}
                className="object-contain"
              />
              <div>
                <h1 className="text-2xl font-bold" style={{ color: '#0B4C02' }}>
                  Produtos
                </h1>
                {parceiro && (
                  <div>
                    <p className="text-sm mt-1" style={{ color: '#4B5563' }}>
                      Bem-vindo, <span className="font-medium">{parceiro.NOMEPARC}</span>
                    </p>
                    {limiteGastos && (
                      <div className="text-xs mt-1 flex gap-4" style={{ color: '#6B7280' }}>
                        <span> Limite: R$ {limiteGastos.limiteTotal.toFixed(2)}</span>
                        <span> Gasto: R$ {limiteGastos.gastoAtual.toFixed(2)}</span>
                        <span className={`font-semibold ${limiteGastos.saldoDisponivel > 50 ? 'text-green-600' : limiteGastos.saldoDisponivel > 20 ? 'text-yellow-600' : 'text-red-600'}`}>
                           Disponível: R$ {limiteGastos.saldoDisponivel.toFixed(2)}
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* Botão do histórico */}
              <button
                onClick={carregarHistoricoPedidos}
                disabled={isCarregandoHistorico}
                className="text-white px-4 py-2 rounded-lg transition-colors font-medium text-sm flex items-center gap-2 disabled:opacity-50"
                style={{ backgroundColor: '#0B4C02' }}
                onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = '#0A4001')}
                onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = '#0B4C02')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                  <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                </svg>
                {isCarregandoHistorico ? 'Carregando...' : 'Histórico'}
              </button>

              {/* Botão do carrinho */}
              <button
                onClick={() => setMostrarCarrinho(!mostrarCarrinho)}
                className="relative text-white px-4 py-2 rounded-lg transition-colors font-medium text-sm flex items-center gap-2"
                style={{ backgroundColor: '#0B4C02' }}
                onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#0A4001')}
                onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = '#0B4C02')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                </svg>
                Carrinho
                {carrinho.length > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {carrinho.length}
                  </span>
                )}
              </button>

              {/* Botão de logout */}
              <button
                onClick={logout}
                className="text-white px-4 py-2 rounded-lg transition-colors font-medium text-sm flex items-center gap-2"
                style={{ backgroundColor: '#0B4C02' }}
                onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#0A4001')}
                onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = '#0B4C02')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path fillRule="evenodd" d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2z"/>
                  <path fillRule="evenodd" d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
                </svg>
                Sair
              </button>
            </div>
          </div>

          <div className="relative w-full sm:w-80">
            <input
              type="text"
              placeholder="Pesquisar produtos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border-2 rounded-lg focus:outline-none focus:ring-2"
              style={{ borderColor: '#0B4C02' }}
            />
            <svg
              className="absolute right-3 top-2.5 h-5 w-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>

          <div className="mt-4 text-sm text-gray-600">
            {isLoading && 'Carregando produtos...'}
          </div>
        </div>

        {message && (
          <div className={`mb-6 p-4 rounded-lg ${message.type === 'success'
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 text-red-800 border border-red-200'
            }`} style={message.type === 'success' ? { color: '#0B4C02' } : {}}>
            {message.text}
          </div>
        )}

        {isLoading && (
          <div className="bg-white rounded-2xl shadow-2xl p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" style={{ borderColor: '#0B4C02' }}></div>
            <p className="text-gray-600">Carregando produtos...</p>
          </div>
        )}

        {!isLoading && (
          <div className="bg-white rounded-2xl shadow-2xl p-6">
            {produtosFiltrados.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                {searchTerm ? 'Nenhum produto encontrado para a pesquisa.' : 'Nenhum produto disponível.'}
              </div>
            ) : (
              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 gap-2 sm:gap-3 md:gap-4 lg:gap-6">
                {produtosFiltrados.map((produto) => (
                  <div key={produto.CODPROD} className="bg-white border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                    {/* Imagem do produto */}
                    <div className="aspect-square w-full bg-white rounded-t-lg overflow-hidden border-b border-gray-100">
                      <ImagemProduto produto={produto} className="w-full h-full object-contain p-1 sm:p-2 md:p-3" />
                    </div>

                    {/* Informações do produto */}
                    <div className="p-1.5 sm:p-2 md:p-3">
                      {/* Nome do produto */}
                      <h3 className="text-xs font-medium text-gray-900 mb-1 min-h-[1.5rem] sm:min-h-[2rem] overflow-hidden"
                          style={{
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical'
                          }}>
                        {produto.DESCRPROD}
                      </h3>

                      {/* Código */}
                      <p className="text-xs mb-1 hidden sm:block" style={{ color: '#6B7280' }}>
                        Código: {produto.CODPROD}
                      </p>

                      {/* Preço */}
                      <p className="text-sm font-bold mb-1 sm:mb-2" style={{ color: '#0B4C02' }}>
                        {formatarPreco(produto.VLRVENDA)}
                      </p>

                      {/* Status e botão */}
                      <div className="space-y-1">
                        <div className="flex justify-center">
                          <span className={`inline-flex px-1.5 sm:px-2 py-0.5 text-xs font-semibold rounded-full ${
                            produto.DISPONIVEL > 0
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {formatarEstoque(produto.DISPONIVEL)}
                          </span>
                        </div>

                        <button
                          onClick={() => adicionarAoCarrinho(produto)}
                          disabled={produto.DISPONIVEL <= 0}
                          className="w-full text-white py-1 sm:py-1.5 px-1 sm:px-2 rounded text-xs font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          style={{ backgroundColor: '#0B4C02' }}
                          onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = '#0A4001')}
                          onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = '#0B4C02')}
                        >
                          {produto.DISPONIVEL > 0 ? 'Adicionar' : 'Sem Estoque'}
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        <div className="mt-6 text-center">
          <button
            onClick={carregarProdutos}
            disabled={isLoading}
            className="text-white px-6 py-3 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
            style={{ backgroundColor: '#0B4C02' }}
            onMouseEnter={(e) => !isLoading && (e.currentTarget.style.backgroundColor = '#0A4001')}
            onMouseLeave={(e) => !isLoading && (e.currentTarget.style.backgroundColor = '#0B4C02')}
          >
            {isLoading ? 'Carregando...' : 'Recarregar Produtos'}
          </button>
        </div>

        {/* Modal do Carrinho */}
        {mostrarCarrinho && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end sm:items-center justify-center z-50 p-0 sm:p-4" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
            <div className="bg-white rounded-t-2xl sm:rounded-2xl shadow-2xl w-full max-w-2xl h-[90vh] sm:max-h-[85vh] overflow-hidden flex flex-col">
              {/* Header do carrinho */}
              <div className="p-4 sm:p-6 border-b flex-shrink-0" style={{ backgroundColor: '#0B4C02' }}>
                <div className="flex items-center justify-between">
                  <h2 className="text-lg sm:text-xl font-bold text-white">Carrinho de Compras</h2>
                  <button
                    onClick={() => setMostrarCarrinho(false)}
                    className="text-white hover:text-gray-200 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"/>
                    </svg>
                  </button>
                </div>
              </div>

              {/* Conteúdo do carrinho */}
              <div className="p-4 sm:p-6 flex-1 overflow-y-auto" style={{ WebkitOverflowScrolling: 'touch' }}>
                {carrinho.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16" className="mx-auto mb-4">
                      <path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                    </svg>
                    <p>Seu carrinho está vazio</p>
                    <p className="text-sm mt-2">Adicione produtos para continuar</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {carrinho.map((item) => (
                      <div key={item.produto.CODPROD} className="flex items-center gap-4 p-4 border rounded-lg">
                        <div className="w-12 h-12 flex items-center justify-center bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                          <ImagemProduto produto={item.produto} />
                        </div>

                        <div className="flex-1">
                          <h3 className="font-medium" style={{ color: '#111827' }}>{item.produto.DESCRPROD}</h3>
                          <p className="text-sm" style={{ color: '#4B5563' }}>Código: {item.produto.CODPROD}</p>
                          <p className="text-sm font-semibold" style={{ color: '#0B4C02' }}>
                            {formatarPreco(item.produto.VLRVENDA)} cada
                          </p>
                        </div>

                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => atualizarQuantidade(item.produto.CODPROD, item.quantidade - 1)}
                              className="w-8 h-8 rounded-full border-2 flex items-center justify-center text-gray-600 hover:bg-gray-100"
                              style={{ borderColor: '#0B4C02' }}
                            >
                              -
                            </button>
                            <span className="w-12 text-center font-medium">{item.quantidade}</span>
                            <button
                              onClick={() => atualizarQuantidade(item.produto.CODPROD, item.quantidade + 1)}
                              className="w-8 h-8 rounded-full border-2 flex items-center justify-center text-white"
                              style={{ backgroundColor: '#0B4C02', borderColor: '#0B4C02' }}
                            >
                              +
                            </button>
                          </div>

                          <div className="text-right">
                            <p className="font-semibold">{formatarPreco(item.produto.VLRVENDA * item.quantidade)}</p>
                          </div>

                          <button
                            onClick={() => removerDoCarrinho(item.produto.CODPROD)}
                            className="text-red-500 hover:text-red-700 ml-2"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                              <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                              <path fillRule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                            </svg>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Footer do carrinho */}
              {carrinho.length > 0 && (
                <div className="p-3 sm:p-4 md:p-6 border-t bg-gray-50 flex-shrink-0">
                  <div className="flex items-center justify-between mb-1 sm:mb-2">
                    <span className="text-sm sm:text-base md:text-lg font-semibold">Total:</span>
                    <span className="text-base sm:text-lg md:text-xl font-bold" style={{ color: '#0B4C02' }}>
                      {formatarPreco(calcularTotalCarrinho())}
                    </span>
                  </div>

                  {limiteGastos && (
                    <div className="mb-2 sm:mb-3 md:mb-4">
                      <div className="flex items-center justify-between text-xs sm:text-sm mb-1" style={{ color: '#4B5563' }}>
                        <span>Saldo disponível:</span>
                        <span className={`font-semibold ${limiteGastos.saldoDisponivel >= calcularTotalCarrinho() ? 'text-green-600' : 'text-red-600'}`}>
                          R$ {limiteGastos.saldoDisponivel.toFixed(2)}
                        </span>
                      </div>
                      {calcularTotalCarrinho() > limiteGastos.saldoDisponivel && (
                        <div className="text-xs text-red-600 bg-red-50 p-1.5 sm:p-2 rounded">
                          ⚠️ <span className="hidden sm:inline">Valor do carrinho excede o saldo disponível</span><span className="sm:hidden">Valor excede o saldo</span>
                        </div>
                      )}
                    </div>
                  )}

                  <button
                    onClick={finalizarPedido}
                    disabled={isFinalizandoPedido || (limiteGastos ? calcularTotalCarrinho() > limiteGastos.saldoDisponivel : false)}
                    className="w-full text-white py-2 sm:py-2.5 md:py-3 px-4 rounded sm:rounded-lg text-sm sm:text-base font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{ backgroundColor: '#0B4C02' }}
                    onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = '#0A4001')}
                    onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = '#0B4C02')}
                  >
                    {isFinalizandoPedido ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        Finalizando Pedido...
                      </div>
                    ) : (
                      'Finalizar Pedido'
                    )}
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Modal do Histórico de Pedidos */}
        {mostrarHistorico && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end sm:items-center justify-center z-50 p-0 sm:p-4" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
            <div className="bg-white rounded-t-2xl sm:rounded-2xl shadow-2xl w-full max-w-4xl h-[90vh] sm:max-h-[85vh] overflow-hidden flex flex-col">
              {/* Header do histórico */}
              <div className="p-4 sm:p-6 border-b flex-shrink-0" style={{ backgroundColor: '#0B4C02' }}>
                <div className="flex items-center justify-between">
                  <h2 className="text-lg sm:text-xl font-bold text-white">Histórico de Pedidos</h2>
                  <button
                    onClick={() => setMostrarHistorico(false)}
                    className="text-white hover:text-gray-200 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"/>
                    </svg>
                  </button>
                </div>
              </div>

              {/* Conteúdo do histórico */}
              <div className="p-4 sm:p-6 flex-1 overflow-y-auto" style={{ WebkitOverflowScrolling: 'touch' }}>
                {historicoPedidos.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16" className="mx-auto mb-4">
                      <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                      <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                    </svg>
                    <p>Nenhum pedido encontrado</p>
                    <p className="text-sm mt-2">Você ainda não fez nenhum pedido</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {historicoPedidos.map((pedido) => (
                      <div key={pedido.nunota} className="border rounded-lg p-4 bg-gray-50">
                        {/* Header do pedido */}
                        <div className="flex items-center justify-between mb-4 pb-3 border-b">
                          <div>
                            <h3 className="font-semibold text-lg">Pedido #{pedido.nunota}</h3>
                            <p className="text-sm" style={{ color: '#4B5563' }}>Parceiro: {pedido.codparc}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-xl font-bold" style={{ color: '#0B4C02' }}>
                              {formatarPreco(pedido.vlrnota)}
                            </p>
                            <button
                              onClick={() => refazerPedido(pedido)}
                              className="mt-2 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                              style={{ backgroundColor: '#0B4C02' }}
                              onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#0A4001')}
                              onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = '#0B4C02')}
                            >
                              🔄 Refazer Pedido
                            </button>
                          </div>
                        </div>

                        {/* Itens do pedido */}
                        <div className="space-y-2">
                          <h4 className="font-medium mb-2" style={{ color: '#374151' }}>Itens do pedido:</h4>
                          {pedido.itens.map((item, index) => (
                            <div key={`${pedido.nunota}-${item.codprod}-${index}`} className="flex items-center justify-between py-2 px-3 bg-white rounded border">
                              <div className="flex-1">
                                <p className="font-medium">{item.descrprod}</p>
                                <p className="text-sm" style={{ color: '#4B5563' }}>Código: {item.codprod}</p>
                              </div>
                              <div className="text-right">
                                <p className="font-semibold" style={{ color: '#0B4C02' }}>
                                  {formatarPreco(item.vlrunit)} x {item.qtdneg}
                                </p>
                                <p className="text-xs" style={{ color: '#6B7280' }}>
                                  Total: {formatarPreco(item.vlrunit * item.qtdneg)}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Botão do Carrinho */}
      {mostrarBotaoTopo && (
        <button
          onClick={() => setMostrarCarrinho(true)}
          style={{
            position: 'fixed',
            bottom: '88px',
            right: '24px',
            width: '48px',
            height: '48px',
            backgroundColor: '#0B4C02',
            color: 'white',
            border: 'none',
            borderRadius: '50%',
            fontSize: '18px',
            zIndex: 9999,
            cursor: 'pointer',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#0A4001')}
          onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = '#0B4C02')}
          title="Abrir carrinho"
        >
          🛒
          {carrinho.length > 0 && (
            <span style={{
              position: 'absolute',
              top: '-8px',
              right: '-8px',
              backgroundColor: '#EF4444',
              color: 'white',
              fontSize: '12px',
              fontWeight: 'bold',
              borderRadius: '50%',
              width: '20px',
              height: '20px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {carrinho.reduce((total, item) => total + item.quantidade, 0)}
            </span>
          )}
        </button>
      )}

      {/* Botão Voltar ao Topo */}
      {mostrarBotaoTopo && (
        <button
          onClick={voltarAoTopo}
          style={{
            position: 'fixed',
            bottom: '24px',
            right: '24px',
            width: '48px',
            height: '48px',
            backgroundColor: '#0B4C02',
            color: 'white',
            border: 'none',
            borderRadius: '50%',
            fontSize: '18px',
            zIndex: 9998,
            cursor: 'pointer',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#0A4001')}
          onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = '#0B4C02')}
          title="Voltar ao topo"
        >
          ↑
        </button>
      )}

      {/* Modal de Imagem */}
      {imagemModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setImagemModal(null)}
        >
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setImagemModal(null)}
              className="absolute -top-10 right-0 text-white text-2xl hover:text-gray-300 z-10"
              title="Fechar"
            >
              ✕
            </button>

            <div className="bg-white p-4 rounded-lg shadow-2xl">
              <div className="mb-3">
                <h3 className="text-lg font-semibold text-gray-900">{imagemModal.produto.DESCRPROD}</h3>
                <p className="text-sm text-gray-600">Código: {imagemModal.produto.CODPROD}</p>
                <p className="text-sm font-medium" style={{ color: '#0B4C02' }}>
                  {formatarPreco(imagemModal.produto.VLRVENDA)}
                </p>
              </div>

              <img
                src={imagemModal.url}
                alt={imagemModal.produto.DESCRPROD}
                className="max-w-full max-h-[70vh] object-contain rounded"
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>
        </div>
      )}

      {/* Popup de Sucesso da Compra */}
      {mostrarPopupSucesso && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" style={{ backgroundColor: 'rgba(0, 0, 0, 0.6)' }}>
          <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 text-center animate-pulse">
            {/* Ícone de sucesso */}
            <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style={{ backgroundColor: '#0B4C02' }}>
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="white" viewBox="0 0 16 16">
                <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
              </svg>
            </div>

            {/* Título */}
            <h2 className="text-2xl font-bold mb-3" style={{ color: '#0B4C02' }}>
              🎉 Compra Realizada!
            </h2>

            {/* Mensagem */}
            <p className="text-gray-600 mb-4 text-lg">
              Seu pedido foi confirmado com sucesso!
            </p>

            {/* Submensagem */}
            <p className="text-sm text-gray-500 mb-6">
              Você será redirecionado para a página inicial em instantes...
            </p>

            {/* Barra de progresso */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div
                className="h-2 rounded-full animate-pulse"
                style={{
                  backgroundColor: '#0B4C02',
                  animation: 'progress 3s linear forwards'
                }}
              ></div>
            </div>

            {/* Texto de redirecionamento */}
            <p className="text-xs text-gray-400">
              Aguarde enquanto preparamos sua próxima experiência...
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
