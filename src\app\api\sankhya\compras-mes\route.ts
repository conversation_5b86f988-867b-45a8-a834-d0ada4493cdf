import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
  console.log('🔍 API Route /api/sankhya/compras-mes - Iniciando requisição');
  
  try {
    const { bearerToken, codparc } = await request.json();
    console.log('🔑 Token recebido:', bearerToken ? 'Token presente' : 'Token ausente');
    console.log('👤 CODPARC recebido:', codparc);

    if (!bearerToken) {
      console.error('❌ Token de autenticação não fornecido');
      return NextResponse.json(
        { error: 'Token de autenticação é obrigatório' },
        { status: 400 }
      );
    }

    if (!codparc) {
      console.error('❌ CODPARC não fornecido');
      return NextResponse.json(
        { error: 'CODPARC é obrigatório' },
        { status: 400 }
      );
    }

    const SANKHYA_API_URL = process.env.SANKHYA_CRUD_LOAD_URL;
    console.log('🌐 URL da API Sankhya:', SANKHYA_API_URL);

    if (!SANKHYA_API_URL) {
      return NextResponse.json(
        { error: 'URL da API Sankhya não configurada no servidor' },
        { status: 500 }
      );
    }

    // Calcular primeiro dia do mês atual
    const hoje = new Date();
    const primeiroDiaDoMes = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
    const dataFormatada = primeiroDiaDoMes.toLocaleDateString('pt-BR');
    console.log('📅 Data de início do mês:', dataFormatada);

    const requestBody = {
      serviceName: "CRUDServiceProvider.loadRecords",
      requestBody: {
        dataSet: {
          rootEntity: "CabecalhoNota",
          includePresentationFields: "S",
          offsetPage: "0",
          criteria: {
            expression: {
              $: "(this.CODPARC = ? AND this.DTNEG >= ?)"
            },
            parameter: [
              {
                $: codparc.toString(),
                type: "I"
              },
              {
                $: dataFormatada,
                type: "D"
              }
            ]
          },
          entity: {
            fieldset: {
              list: "NUNOTA,CODEMP,CODPARC,DTNEG,VLRNOTA"
            }
          }
        }
      }
    };

    console.log('📤 Corpo da requisição:', JSON.stringify(requestBody, null, 2));
    console.log('📡 Fazendo requisição para API Sankhya...');

    const response = await axios.post(SANKHYA_API_URL, requestBody, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📥 Resposta da API Sankhya:', {
      status: response.status,
      statusText: response.statusText,
      hasData: !!response.data,
      dataKeys: response.data ? Object.keys(response.data) : []
    });

    console.log('📊 Dados completos da resposta:', JSON.stringify(response.data, null, 2));

    return NextResponse.json({
      success: true,
      data: response.data
    });

  } catch (error) {
    console.error('💥 Erro na API Route /api/sankhya/compras-mes:', error);
    
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      console.error('📡 Detalhes do erro HTTP:', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        headers: axiosError.response?.headers
      });
      
      return NextResponse.json(
        {
          error: `Erro na API Sankhya: ${axiosError.response?.status} - ${axiosError.response?.data?.message || axiosError.response?.statusText}`,
          success: false,
          details: axiosError.response?.data
        },
        { status: axiosError.response?.status || 500 }
      );
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Erro desconhecido ao buscar compras do mês',
        success: false
      },
      { status: 500 }
    );
  }
}
