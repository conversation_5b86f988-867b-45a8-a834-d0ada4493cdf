import { NextRequest, NextResponse } from 'next/server';
import { incluirNotaCompleto } from '@/services/sankhyaApi';

export async function POST(request: NextRequest) {
  console.log('🔍 API Route /api/sankhya/incluir-nota-completo - Iniciando requisição');
  
  try {
    const body = await request.json();
    console.log('📥 Dados recebidos:', {
      codparc: body.codparc,
      itensCount: body.itens?.length || 0
    });

    const { codparc, itens } = body;

    if (!codparc) {
      console.error('❌ CODPARC não fornecido');
      return NextResponse.json({
        success: false,
        error: 'CODPARC é obrigatório'
      }, { status: 400 });
    }

    if (!itens || !Array.isArray(itens) || itens.length === 0) {
      console.error('❌ Itens não fornecidos ou inválidos');
      return NextResponse.json({
        success: false,
        error: 'Itens são obrigatórios'
      }, { status: 400 });
    }

    console.log('🚀 Iniciando fluxo completo de inclusão de nota...');
    const resultado = await incluirNotaCompleto(codparc, itens);

    console.log('✅ Fluxo completo finalizado com sucesso');
    console.log('📄 NUNOTA gerado:', resultado.nunota);

    return NextResponse.json({
      success: true,
      nunota: resultado.nunota,
      data: resultado
    });

  } catch (error) {
    console.error('💥 Erro na API incluir-nota-completo:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';
    
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
}
