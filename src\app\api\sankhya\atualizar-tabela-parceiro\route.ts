import { NextResponse } from 'next/server';
import axios from 'axios';

// Interfaces para as respostas da API
interface SankhyaApiResponse {
  responseBody?: {
    entities?: {
      entity?: {
        f0?: { $: string };
        f1?: { $: string };
        f2?: { $: string };
      };
    };
  };
}

export async function POST(request: Request) {
  console.log('🔄 API Route /api/sankhya/atualizar-tabela-parceiro - Iniciando requisição');

  try {
    const { bearerToken, codparc } = await request.json();
    console.log('🔑 Token recebido:', bearerToken ? 'Token presente' : 'Token ausente');
    console.log('👤 CODPARC recebido:', codparc);

    if (!bearerToken) {
      console.error('❌ Token de autenticação não fornecido');
      return NextResponse.json(
        { error: 'Token de autenticação é obrigatório' },
        { status: 400 }
      );
    }

    if (!codparc) {
      console.error('❌ CODPARC não fornecido');
      return NextResponse.json(
        { error: 'CODPARC é obrigatório' },
        { status: 400 }
      );
    }

    const SANKHYA_ATUALIZAR_URL = process.env.SANKHYA_ATUALIZAR_TABELA_PARCEIRO;
    const SANKHYA_CRUD_LOAD_URL = process.env.SANKHYA_CRUD_LOAD_URL;
    console.log('🌐 URL Atualizar:', SANKHYA_ATUALIZAR_URL);
    console.log('🌐 URL CRUD Load:', SANKHYA_CRUD_LOAD_URL);

    if (!SANKHYA_ATUALIZAR_URL || !SANKHYA_CRUD_LOAD_URL) {
      console.error('❌ URLs não configuradas:', {
        SANKHYA_ATUALIZAR_URL: !!SANKHYA_ATUALIZAR_URL,
        SANKHYA_CRUD_LOAD_URL: !!SANKHYA_CRUD_LOAD_URL
      });
      return NextResponse.json(
        { error: 'URLs da API Sankhya não configuradas no servidor' },
        { status: 500 }
      );
    }

    // 1. Primeiro, buscar dados atuais do Parceiro para verificar LIMCRED
    console.log('🔍 Buscando dados atuais do Parceiro...');
    const buscarParceiroBody = {
      serviceName: "CRUDServiceProvider.loadRecords",
      requestBody: {
        dataSet: {
          rootEntity: "Parceiro",
          includePresentationFields: "N",
          offsetPage: "0",
          criteria: {
            expression: {
              "$": "this.CODPARC = ?"
            },
            parameter: [
              {
                "$": codparc.toString(),
                "type": "I"
              }
            ]
          },
          entity: {
            fieldset: {
              list: "CODPARC,LIMCRED,CODTAB"
            }
          }
        }
      }
    };

    const parceiroResponse = await axios.post<SankhyaApiResponse>(SANKHYA_CRUD_LOAD_URL, buscarParceiroBody, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json'
      }
    });

    // 2. Buscar dados do ComplementoParc para verificar SUGTIPNEGSAID
    console.log('🔍 Buscando dados do ComplementoParc...');
    const buscarComplementoBody = {
      serviceName: "CRUDServiceProvider.loadRecords",
      requestBody: {
        dataSet: {
          rootEntity: "ComplementoParc",
          includePresentationFields: "N",
          offsetPage: "0",
          criteria: {
            expression: {
              "$": "this.CODPARC = ?"
            },
            parameter: [
              {
                "$": codparc.toString(),
                "type": "I"
              }
            ]
          },
          entity: {
            fieldset: {
              list: "CODPARC,SUGTIPNEGSAID"
            }
          }
        }
      }
    };

    const complementoResponse = await axios.post<SankhyaApiResponse>(SANKHYA_CRUD_LOAD_URL, buscarComplementoBody, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json'
      }
    });

    // 3. Processar dados do Parceiro e verificar CODTAB e LIMCRED
    console.log('📊 Processando dados do Parceiro...');
    console.log('📊 Dados completos do Parceiro:', JSON.stringify(parceiroResponse.data, null, 2));

    const parceiroData = parceiroResponse.data?.responseBody?.entities?.entity;
    let needsLimcredUpdate = false;
    let needsCodtabUpdate = false;
    let limcredAtual = null;
    let codtabAtual = null;

    if (parceiroData) {
      // f0 = CODPARC, f1 = LIMCRED, f2 = CODTAB
      limcredAtual = parceiroData.f1?.$ ? parseFloat(parceiroData.f1.$) : null;
      codtabAtual = parceiroData.f2?.$ ? parseInt(parceiroData.f2.$) : null;

      console.log('💰 LIMCRED atual:', limcredAtual);
      console.log('🏷️ CODTAB atual:', codtabAtual);

      // Verificar se LIMCRED é nulo ou menor que 220
      if (limcredAtual === null || limcredAtual < 220) {
        needsLimcredUpdate = true;
        console.log('⚠️ LIMCRED precisa ser atualizado para 220');
      } else {
        console.log('✅ LIMCRED já está adequado:', limcredAtual);
      }

      // Verificar se CODTAB é diferente de 1500
      if (codtabAtual !== 1500) {
        needsCodtabUpdate = true;
        console.log('⚠️ CODTAB precisa ser atualizado para 1500');
      } else {
        console.log('✅ CODTAB já está adequado:', codtabAtual);
      }
    } else {
      console.log('⚠️ Dados do Parceiro não encontrados, assumindo que ambos precisam ser atualizados');
      needsLimcredUpdate = true;
      needsCodtabUpdate = true;
    }

    // 4. Processar dados do ComplementoParc e verificar SUGTIPNEGSAID
    console.log('📊 Processando dados do ComplementoParc...');
    const complementoData = complementoResponse.data?.responseBody?.entities?.entity;
    let needsComplementoUpdate = false;
    let sugtipnegsaidAtual = null;

    if (complementoData && complementoData.f1) {
      sugtipnegsaidAtual = complementoData.f1.$ ? parseInt(complementoData.f1.$) : null;
      console.log('🔢 SUGTIPNEGSAID atual:', sugtipnegsaidAtual);

      // Verificar se SUGTIPNEGSAID é diferente de 11
      if (sugtipnegsaidAtual !== 11) {
        needsComplementoUpdate = true;
        console.log('⚠️ SUGTIPNEGSAID precisa ser atualizado para 11');
      }
    }

    const responses = [];

    // 5. Atualizar Parceiro (apenas campos que precisam ser alterados)
    console.log('🔄 Verificando se Parceiro precisa ser atualizado...');
    console.log('🔄 CODTAB precisa ser atualizado?', needsCodtabUpdate);
    console.log('🔄 LIMCRED precisa ser atualizado?', needsLimcredUpdate);

    // Só atualizar se pelo menos um campo precisar ser alterado
    if (needsCodtabUpdate || needsLimcredUpdate) {
      const parceiroFields = ["CODPARC"];
      const parceiroValues: { [key: string]: string } = {};
      let valueIndex = 1;

      if (needsCodtabUpdate) {
        parceiroFields.push("CODTAB");
        parceiroValues[valueIndex.toString()] = "1500";
        valueIndex++;
        console.log('🏷️ Adicionando CODTAB = 1500 à atualização');
      }

      if (needsLimcredUpdate) {
        parceiroFields.push("LIMCRED");
        parceiroValues[valueIndex.toString()] = "220";
        console.log('💰 Adicionando LIMCRED = 220 à atualização');
      }

      console.log('📋 Campos a serem atualizados:', parceiroFields);
      console.log('� Valores a serem definidos:', parceiroValues);

      const parceiroRequestBody = {
      serviceName: "DatasetSP.save",
      requestBody: {
        entityName: "Parceiro",
        standAlone: false,
        fields: parceiroFields,
        records: [
          {
            pk: {
              CODPARC: codparc.toString()
            },
            values: parceiroValues
          }
        ]
      }
    };

    console.log('📤 Corpo da requisição Parceiro:', JSON.stringify(parceiroRequestBody, null, 2));
    console.log('🔄 Enviando requisição para atualizar Parceiro...');

    const parceiroUpdateResponse = await axios.post(SANKHYA_ATUALIZAR_URL, parceiroRequestBody, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📥 Resposta da atualização do Parceiro:', {
      status: parceiroUpdateResponse.status,
      statusText: parceiroUpdateResponse.statusText,
      data: JSON.stringify(parceiroUpdateResponse.data, null, 2)
    });

      const updatedFields = [];
      if (needsCodtabUpdate) updatedFields.push('CODTAB');
      if (needsLimcredUpdate) updatedFields.push('LIMCRED');

      responses.push({
        entity: 'Parceiro',
        updated: true,
        fields: updatedFields,
        response: parceiroUpdateResponse.data
      });
    } else {
      console.log('✅ Parceiro não precisa ser atualizado - CODTAB e LIMCRED já estão corretos');
      responses.push({
        entity: 'Parceiro',
        updated: false,
        reason: 'CODTAB e LIMCRED já estão corretos',
        currentValues: {
          codtab: codtabAtual,
          limcred: limcredAtual
        }
      });
    }

    // 6. Atualizar ComplementoParc se necessário
    if (needsComplementoUpdate) {
      console.log('🔄 Atualizando tabela ComplementoParc...');

      const complementoRequestBody = {
        serviceName: "DatasetSP.save",
        requestBody: {
          entityName: "ComplementoParc",
          standAlone: false,
          fields: [
            "CODPARC",
            "SUGTIPNEGSAID"
          ],
          records: [
            {
              pk: {
                CODPARC: codparc.toString()
              },
              values: {
                "1": "11"
              }
            }
          ]
        }
      };

      console.log('📤 Corpo da requisição ComplementoParc:', JSON.stringify(complementoRequestBody, null, 2));

      const complementoUpdateResponse = await axios.post(SANKHYA_ATUALIZAR_URL, complementoRequestBody, {
        headers: {
          'Authorization': `Bearer ${bearerToken}`,
          'Content-Type': 'application/json'
        }
      });

      responses.push({
        entity: 'ComplementoParc',
        updated: true,
        fields: ['SUGTIPNEGSAID'],
        response: complementoUpdateResponse.data
      });
    } else {
      responses.push({
        entity: 'ComplementoParc',
        updated: false,
        reason: 'SUGTIPNEGSAID já é 11'
      });
    }

    console.log('✅ Todas as atualizações concluídas');

    return NextResponse.json({
      success: true,
      data: responses,
      summary: {
        codparc: codparc,
        parceiroUpdated: {
          codtab: needsCodtabUpdate,
          limcred: needsLimcredUpdate,
          codtabValue: needsCodtabUpdate ? 1500 : codtabAtual,
          limcredValue: needsLimcredUpdate ? 220 : limcredAtual
        },
        complementoUpdated: {
          sugtipnegsaid: needsComplementoUpdate,
          sugtipnegsaidValue: needsComplementoUpdate ? 11 : sugtipnegsaidAtual
        }
      }
    });

  } catch (error) {
    console.error('💥 Erro na API Route /api/sankhya/atualizar-tabela-parceiro:', error);
    
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      console.error('📡 Detalhes do erro HTTP:', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        headers: axiosError.response?.headers
      });
      
      return NextResponse.json(
        {
          error: `Erro na API Sankhya: ${axiosError.response?.status} - ${axiosError.response?.data?.message || axiosError.response?.statusText}`,
          success: false,
          details: axiosError.response?.data
        },
        { status: axiosError.response?.status || 500 }
      );
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Erro desconhecido ao atualizar tabela do parceiro',
        success: false
      },
      { status: 500 }
    );
  }
}
