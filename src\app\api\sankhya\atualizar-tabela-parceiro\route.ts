import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
  console.log('🔄 API Route /api/sankhya/atualizar-tabela-parceiro - Iniciando requisição');
  
  try {
    const { bearerToken, codparc } = await request.json();
    console.log('🔑 Token recebido:', bearerToken ? 'Token presente' : 'Token ausente');
    console.log('👤 CODPARC recebido:', codparc);

    if (!bearerToken) {
      console.error('❌ Token de autenticação não fornecido');
      return NextResponse.json(
        { error: 'Token de autenticação é obrigatório' },
        { status: 400 }
      );
    }

    if (!codparc) {
      console.error('❌ CODPARC não fornecido');
      return NextResponse.json(
        { error: 'CODPARC é obrigatório' },
        { status: 400 }
      );
    }

    const SANKHYA_ATUALIZAR_URL = process.env.SANKHYA_ATUALIZAR_TABELA_PARCEIRO;
    console.log('🌐 URL da API Sankhya:', SANKHYA_ATUALIZAR_URL);

    if (!SANKHYA_ATUALIZAR_URL) {
      return NextResponse.json(
        { error: 'URL da API Sankhya para atualização não configurada no servidor' },
        { status: 500 }
      );
    }

    const requestBody = {
      serviceName: "DatasetSP.save",
      requestBody: {
        entityName: "Parceiro",
        standAlone: false,
        fields: [
          "CODPARC",
          "CODTAB"
        ],
        records: [
          {
            pk: {
              CODPARC: codparc.toString()
            },
            values: {
              "1": "1500"
            }
          }
        ]
      }
    };

    console.log('📤 Corpo da requisição:', JSON.stringify(requestBody, null, 2));
    console.log('📡 Fazendo requisição para API Sankhya...');

    const response = await axios.post(SANKHYA_ATUALIZAR_URL, requestBody, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📥 Resposta da API Sankhya:', {
      status: response.status,
      statusText: response.statusText,
      hasData: !!response.data,
      dataKeys: response.data ? Object.keys(response.data) : []
    });

    console.log('📊 Dados completos da resposta:', JSON.stringify(response.data, null, 2));

    return NextResponse.json({
      success: true,
      data: response.data
    });

  } catch (error) {
    console.error('💥 Erro na API Route /api/sankhya/atualizar-tabela-parceiro:', error);
    
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      console.error('📡 Detalhes do erro HTTP:', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        headers: axiosError.response?.headers
      });
      
      return NextResponse.json(
        {
          error: `Erro na API Sankhya: ${axiosError.response?.status} - ${axiosError.response?.data?.message || axiosError.response?.statusText}`,
          success: false,
          details: axiosError.response?.data
        },
        { status: axiosError.response?.status || 500 }
      );
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Erro desconhecido ao atualizar tabela do parceiro',
        success: false
      },
      { status: 500 }
    );
  }
}
