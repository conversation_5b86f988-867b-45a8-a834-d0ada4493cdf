// Tipo para mensagens de feedback
export interface FeedbackMessage {
  type: 'success' | 'error';
  text: string;
}

// Tipos para produtos
export interface Produto {
  CODPROD: number;
  DESCRPROD: string;
  VLRVENDA: number;
  DISPONIVEL: number;
  IMAGEM?: string;
}

// Tipos para parceiro/usuário
export interface Parceiro {
  CODPARC: number;
  NOMEPARC: string;
  FORNECEDOR: string;
  CLIENTE: string;
  CODCID: number;
  CLASSIFICMS: string;
  CODTAB: number;
}

export interface ParceiroRaw {
  f0: { $: string }; // CODPARC
  f1: { $: string }; // NOMEPARC
  f2: { $: string }; // FORNECEDOR
  f3: { $: string }; // CLIENTE
  f4: { $: string }; // CODCID
  f5: { $: string }; // CLASSIFICMS
  f6: { $: string }; // CODTAB
}

export interface ParceiroResponse {
  responseBody: {
    entities: {
      entity: ParceiroRaw;
    };
  };
}

// Tipos para carrinho de compras
export interface ItemCarrinho {
  produto: Produto;
  quantidade: number;
}

// Tipos para pedido/nota
export interface NotaRequest {
  serviceName: string;
  requestBody: {
    nota: {
      cabecalho: {
        NUNOTA: {};
        CODPARC: { $: string };
        DTNEG: { $: string };
        CODTIPOPER: { $: string };
        CODTIPVENDA: { $: string };
        CODVEND: { $: string };
        CODEMP: { $: string };
        TIPMOV: { $: string };
        CODCENCUS: { $: string };
        CODNAT: { $: string };
        OBSERVACAO: { $: string };
      };
      itens: {
        INFORMARPRECO: string;
        item: {
          NUNOTA: {};
          IGNOREDESCPROMOQTD: { $: string };
          CODPROD: { $: string };
          QTDNEG: { $: string };
          CODLOCALORIG: { $: string };
          CODVOL: { $: string };
        }[];
      };
    };
  };
}

// Tipos para compras do mês
export interface CompraMes {
  NUNOTA: number;
  CODEMP: number;
  CODPARC: number;
  DTNEG: string;
  VLRNOTA: number;
}

export interface CompraMesRaw {
  f0: { $: string }; // NUNOTA
  f1: { $: string }; // CODEMP
  f2: { $: string }; // CODPARC
  f3: { $: string }; // DTNEG
  f4: { $: string }; // VLRNOTA
  f5?: { $: string }; // Empresa_NOMEFANTASIA
  f6?: { $: string }; // Parceiro_NOMEPARC
}

export interface ComprasMesResponse {
  responseBody: {
    entities: {
      total: string;
      hasMoreResult: string;
      offsetPage: string;
      offset: string;
      metadata: {
        fields: {
          field: { name: string }[];
        };
      };
      entity?: CompraMesRaw | CompraMesRaw[];
    };
  };
}

// Tipos para limite de gastos
export interface LimiteGastos {
  limiteTotal: number;
  gastoAtual: number;
  saldoDisponivel: number;
  comprasDoMes: CompraMes[];
}

export interface ProdutosResponse {
  responseBody: {
    records: {
      record: ProdutoRaw[];
    };
  };
}

// Tipo para o produto como vem da API (com estrutura $)
export interface ProdutoRaw {
  CODPROD: { $: string };
  DESCRPROD: { $: string };
  VLRVENDA: { $: string };
  DISPONIVEL: { $: string };
  IMAGEM?: { $: string };
}

// Tipos para histórico de pedidos
export interface ItemPedido {
  codprod: number;
  descrprod: string;
  vlrunit: number;
  qtdneg: number;
}

export interface HistoricoPedido {
  nunota: number;
  codparc: number;
  vlrnota: number;
  itens: ItemPedido[];
}

export interface HistoricoPedidosApiResponse {
  success: boolean;
  pedidos?: HistoricoPedido[];
  error?: string;
}
