'use client';

import { useState } from 'react';
import { Produto, FeedbackMessage, Parceiro } from '@/types/produtos';
import { buscarProdutosCompleto, buscarParceiroCompleto } from '@/services/sankhyaApi';
import Image from 'next/image';

export default function ListaProdutos() {
  const [produtos, setProdutos] = useState<Produto[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<FeedbackMessage | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [cpf, setCpf] = useState('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [parceiro, setParceiro] = useState<Parceiro | null>(null);
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const formatarCPF = (valor: string) => {
    const apenasNumeros = valor.replace(/\D/g, '');
    return apenasNumeros
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d{1,2})/, '$1-$2')
      .replace(/(-\d{2})\d+?$/, '$1');
  };

  const handleCPFChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const valorFormatado = formatarCPF(e.target.value);
    setCpf(valorFormatado);
  };

  const autenticarUsuario = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsAuthenticating(true);
    setMessage(null);

    const cpfLimpo = cpf.replace(/\D/g, '');

    if (cpfLimpo.length !== 11) {
      setMessage({
        type: 'error',
        text: 'CPF deve conter 11 dígitos'
      });
      setIsAuthenticating(false);
      return;
    }

    try {
      const parceiroEncontrado = await buscarParceiroCompleto(cpfLimpo);
      
      if (parceiroEncontrado) {
        setParceiro(parceiroEncontrado.parceiro);
        setIsLoggedIn(true);
        setMessage({
          type: 'success',
          text: `Bem-vindo, ${parceiroEncontrado.parceiro.NOMEPARC}!`
        });
        await carregarProdutos();
      } else {
        setMessage({
          type: 'error',
          text: 'CPF não encontrado. Verifique se você está cadastrado no sistema.'
        });
      }
    } catch (error) {
      setMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'Erro ao autenticar usuário'
      });
    } finally {
      setIsAuthenticating(false);
    }
  };

  const carregarProdutos = async () => {
    setIsLoading(true);
    setMessage(null);

    try {
      const produtosData = await buscarProdutosCompleto();
      setProdutos(produtosData);
    } catch (error) {
      setMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'Erro ao carregar produtos'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setIsLoggedIn(false);
    setParceiro(null);
    setProdutos([]);
    setCpf('');
    setSearchTerm('');
    setMessage(null);
  };

  const produtosFiltrados = produtos.filter(produto =>
    produto.DESCRPROD.toLowerCase().includes(searchTerm.toLowerCase()) ||
    produto.CODPROD.toString().includes(searchTerm)
  );

  const formatarPreco = (preco: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(preco);
  };

  const formatarEstoque = (quantidade: number) => {
    return new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(quantidade);
  };

  // Tela de login
  if (!isLoggedIn) {
    return (
      <div
        className="min-h-screen flex items-center justify-center p-4"
        style={{
          backgroundImage: 'url(/bg_cepera.png)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md">
          <div className="flex justify-center mb-6">
            <Image
              src="/logo.png"
              alt="Cepêra Logo"
              width={120}
              height={57}
              className="object-contain"
            />
          </div>

          <h1 className="text-2xl font-bold text-center mb-6" style={{ color: '#0B4C02' }}>
            Acesso aos Produtos
          </h1>

          <p className="text-gray-600 text-center mb-6">
            Digite seu CPF para acessar os produtos disponíveis
          </p>

          {message && (
            <div className={`mb-4 p-4 rounded-lg ${message.type === 'success'
                ? 'bg-green-50 border border-green-200'
                : 'bg-red-50 text-red-800 border border-red-200'
              }`} style={message.type === 'success' ? { color: '#0B4C02' } : {}}>
              {message.text}
            </div>
          )}

          <form onSubmit={autenticarUsuario} className="space-y-4">
            <div className="relative">
              <input
                type="text"
                value={cpf}
                onChange={handleCPFChange}
                placeholder="000.000.000-00"
                maxLength={14}
                className="w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 text-center text-lg"
                style={{ borderColor: '#0B4C02' }}
                disabled={isAuthenticating}
              />
              <label className="absolute -top-2 left-3 bg-white px-1 text-sm font-medium text-gray-700">
                CPF
              </label>
            </div>

            <button
              type="submit"
              disabled={isAuthenticating || cpf.replace(/\D/g, '').length !== 11}
              className="w-full text-white py-3 px-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              style={{ backgroundColor: '#0B4C02' }}
              onMouseEnter={(e) => !isAuthenticating && (e.currentTarget.style.backgroundColor = '#0A4001')}
              onMouseLeave={(e) => !isAuthenticating && (e.currentTarget.style.backgroundColor = '#0B4C02')}
            >
              {isAuthenticating ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Verificando...
                </div>
              ) : (
                'Acessar Produtos'
              )}
            </button>
          </form>
        </div>
      </div>
    );
  }

  // Tela de produtos
  return (
    <div
      className="min-h-screen p-4"
      style={{
        backgroundImage: 'url(/bg_cepera.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-2xl shadow-2xl p-6 mb-6">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mb-4">
            <div className="flex items-center gap-4">
              <Image
                src="/logo.png"
                alt="Cepêra Logo"
                width={120}
                height={57}
                className="object-contain"
              />
              <div>
                <h1 className="text-2xl font-bold" style={{ color: '#0B4C02' }}>
                  Produtos para Funcionários
                </h1>
                {parceiro && (
                  <p className="text-sm text-gray-600 mt-1">
                    Bem-vindo, <span className="font-medium">{parceiro.NOMEPARC}</span> (Código: {parceiro.CODPARC})
                  </p>
                )}
              </div>
            </div>

            <button
              onClick={logout}
              className="text-white px-4 py-2 rounded-lg transition-colors font-medium text-sm flex items-center gap-2"
              style={{ backgroundColor: '#0B4C02' }}
              onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#0A4001')}
              onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = '#0B4C02')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path fillRule="evenodd" d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2z"/>
                <path fillRule="evenodd" d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
              </svg>
              Sair
            </button>
          </div>

          <div className="relative w-full sm:w-80">
            <input
              type="text"
              placeholder="Pesquisar produtos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border-2 rounded-lg focus:outline-none focus:ring-2"
              style={{ borderColor: '#0B4C02' }}
            />
            <svg
              className="absolute right-3 top-2.5 h-5 w-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>

          <div className="mt-4 text-sm text-gray-600">
            {isLoading ? (
              'Carregando produtos...'
            ) : (
              `${produtosFiltrados.length} produto${produtosFiltrados.length !== 1 ? 's' : ''} encontrado${produtosFiltrados.length !== 1 ? 's' : ''}`
            )}
          </div>
        </div>

        {message && (
          <div className={`mb-6 p-4 rounded-lg ${message.type === 'success'
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 text-red-800 border border-red-200'
            }`} style={message.type === 'success' ? { color: '#0B4C02' } : {}}>
            {message.text}
          </div>
        )}

        {isLoading && (
          <div className="bg-white rounded-2xl shadow-2xl p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" style={{ borderColor: '#0B4C02' }}></div>
            <p className="text-gray-600">Carregando produtos...</p>
          </div>
        )}

        {!isLoading && (
          <div className="bg-white rounded-2xl shadow-2xl overflow-hidden">
            {produtosFiltrados.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                {searchTerm ? 'Nenhum produto encontrado para a pesquisa.' : 'Nenhum produto disponível.'}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead style={{ backgroundColor: '#0B4C02' }}>
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-medium text-white uppercase tracking-wider">
                        Código
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-medium text-white uppercase tracking-wider">
                        Descrição
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-medium text-white uppercase tracking-wider">
                        Valor de Venda
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-medium text-white uppercase tracking-wider">
                        Estoque
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {produtosFiltrados.map((produto, index) => (
                      <tr key={produto.CODPROD} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {produto.CODPROD}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {produto.DESCRPROD}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold">
                          {formatarPreco(produto.VLRVENDA)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            produto.DISPONIVEL > 0 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {formatarEstoque(produto.DISPONIVEL)} unidades
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        <div className="mt-6 text-center">
          <button
            onClick={carregarProdutos}
            disabled={isLoading}
            className="text-white px-6 py-3 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
            style={{ backgroundColor: '#0B4C02' }}
            onMouseEnter={(e) => !isLoading && (e.currentTarget.style.backgroundColor = '#0A4001')}
            onMouseLeave={(e) => !isLoading && (e.currentTarget.style.backgroundColor = '#0B4C02')}
          >
            {isLoading ? 'Carregando...' : 'Recarregar Produtos'}
          </button>
        </div>
      </div>
    </div>
  );
}
