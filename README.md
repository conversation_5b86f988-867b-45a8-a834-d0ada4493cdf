# Sistema de Pedidos para Funcionários - Cepêra

Este é um sistema web desenvolvido em Next.js com TypeScript para pedidos de produtos por funcionários, integrado com a API Sankhya.

## Funcionalidades

### 1. Autenticação de Funcionários
- Login por CPF para identificação do funcionário
- Validação automática na base de dados Sankhya
- Controle de acesso baseado no cadastro de parceiros

### 2. Catálogo de Produtos
- Visualização de produtos da view `VW_PRODUTOS_FUNCIONARIOS`
- Busca por código ou descrição do produto
- Informações de preço formatadas em Real (R$)
- Status de disponibilidade dos produtos
- Interface responsiva com tabela organizada

### 3. Carrinho de Compras
- Adição de produtos ao carrinho
- Controle de quantidade por item
- Cálculo automático do valor total
- Visualização do limite de gastos mensal

### 4. Gestão de Pedidos
- Finalização de pedidos com confirmação automática
- Controle de limite mensal de R$ 200,00 por funcionário
- Histórico dos últimos 3 pedidos realizados
- Funcionalidade de refazer pedidos anteriores

### 5. Limite de Gastos
- Consulta automática de gastos do mês atual
- Exibição do saldo disponível
- Bloqueio de pedidos quando limite excedido

## Tecnologias Utilizadas

- **Next.js 15** - Framework React
- **TypeScript** - Tipagem estática
- **React** - Biblioteca para interfaces de usuário
- **Axios** - Cliente HTTP para requisições à API
- **Tailwind CSS** - Framework CSS para estilização

## Configuração

### 1. Instalar dependências

```bash
npm install
```

### 2. Configurar variáveis de ambiente

Copie o arquivo `.env.local.example` para `.env.local` e configure suas credenciais:

```bash
cp .env.local.example .env.local
```

Edite o arquivo `.env.local` com suas credenciais da API Sankhya:

```env
# Configurações da API Sankhya
SANKHYA_LOGIN_URL=https://api.sandbox.sankhya.com.br/login
SANKHYA_LOGOUT_URL=https://api.sandbox.sankhya.com.br/gateway/v1/mge/service.sbr?serviceName=MobileLoginSP.logout&outputType=json

# Credenciais de login da API Sankhya
SANKHYA_USUARIO=<EMAIL>
SANKHYA_SENHA=sua_senha
SANKHYA_TOKEN=seu_token_uuid
SANKHYA_APPKEY=sua_appkey_uuid
```

### 3. Executar o projeto

```bash
npm run dev
```

O sistema estará disponível em `http://localhost:3000`

## Estrutura do Projeto

```
src/
├── app/
│   ├── api/sankhya/          # API routes para integração Sankhya
│   │   ├── login/            # Autenticação na API Sankhya
│   │   ├── logout/           # Logout da API Sankhya
│   │   ├── parceiro/         # Busca de funcionário por CPF
│   │   ├── produtos/         # Busca de produtos disponíveis
│   │   ├── compras-mes/      # Consulta gastos do mês
│   │   ├── incluir-nota/     # Inclusão de pedidos
│   │   ├── confirmar-nota/   # Confirmação de pedidos
│   │   └── historico-pedidos/ # Histórico de pedidos
│   ├── page.tsx              # Página principal do sistema
│   ├── layout.tsx            # Layout da aplicação
│   └── globals.css           # Estilos globais
├── services/
│   └── sankhyaApi.ts         # Serviços da API Sankhya
└── types/
    └── produtos.ts           # Tipos TypeScript
```

## Fluxo de Funcionamento

### 1. Autenticação
1. **Login por CPF**: Funcionário informa seu CPF
2. **Validação**: Sistema busca o funcionário na base Sankhya
3. **Carregamento**: Produtos e limite de gastos são carregados automaticamente

### 2. Realização de Pedidos
1. **Seleção de produtos**: Funcionário adiciona produtos ao carrinho
2. **Verificação de limite**: Sistema valida se há saldo disponível
3. **Finalização**: Pedido é incluído e confirmado automaticamente na Sankhya
4. **Atualização**: Saldo é atualizado em tempo real

### 3. Controle de Gastos
- **Limite mensal**: R$ 200,00 por funcionário
- **Consulta automática**: Gastos do mês atual são verificados
- **Saldo disponível**: Exibido em tempo real na interface

## API Sankhya

### Endpoints Utilizados

#### Login
- **URL**: `https://api.sandbox.sankhya.com.br/login`
- **Método**: POST
- **Headers**: username, password, token, appkey
- **Resposta**: Bearer token para autenticação

#### Busca de Funcionário (Parceiro)
- **Serviço**: `CRUDServiceProvider.loadRecords`
- **Entidade**: `Parceiro`
- **Filtro**: CPF do funcionário
- **Campos**: CODPARC, NOMEPARC, FORNECEDOR, CLIENTE

#### Produtos Disponíveis
- **Serviço**: `CRUDServiceProvider.loadView`
- **View**: `VW_PRODUTOS_FUNCIONARIOS`
- **Campos**: CODPROD, DESCRPROD, VLRVENDA, DISPONIVEL

#### Controle de Gastos
- **Serviço**: `CRUDServiceProvider.loadRecords`
- **Entidade**: `CabecalhoNota`
- **Filtro**: CODPARC + data >= primeiro dia do mês
- **Campos**: VLRNOTA para soma dos gastos

#### Inclusão de Pedidos
- **Serviço**: `CACSP.incluirNota`
- **Dados**: Cabeçalho + itens do pedido
- **Confirmação**: `CACSP.confirmarNota`

#### Histórico de Pedidos
- **Serviço**: `CRUDServiceProvider.loadRecords`
- **Entidades**: `CabecalhoNota` + `ItemNota` (com join Produto)
- **Filtro**: CODPARC + CODTIPOPER = 1000
- **Retorno**: Últimos 3 pedidos com itens detalhados

## Interface do Sistema

### Página Principal (/)
- **Funcionalidade**: Sistema completo de pedidos para funcionários
- **Seções**:
  - **Login**: Autenticação por CPF
  - **Catálogo**: Lista de produtos com busca
  - **Carrinho**: Gestão de itens selecionados
  - **Limite**: Controle de gastos mensais
  - **Histórico**: Últimos pedidos realizados

### Características da Interface
- **Design responsivo**: Funciona em desktop e mobile
- **Busca inteligente**: Por código ou descrição do produto
- **Feedback visual**: Mensagens de sucesso/erro
- **Controle de estoque**: Indicação de disponibilidade
- **Formatação monetária**: Valores em Real brasileiro
- **Atualização automática**: Saldo e dados em tempo real

## Personalização

### Modificar limite de gastos
Altere o valor limite no arquivo `src/services/sankhyaApi.ts` na função de cálculo de limite.

### Adicionar novos campos de produto
Edite o arquivo `src/types/produtos.ts` e atualize as interfaces conforme necessário.

### Customizar estilos
O projeto usa Tailwind CSS. Modifique as classes nos componentes ou adicione estilos customizados em `src/app/globals.css`.

### Configurar novos tipos de operação
Ajuste o filtro `CODTIPOPER` nos endpoints para incluir outros tipos de pedidos.

## Segurança

- **Variáveis de ambiente**: Credenciais da API Sankhya protegidas em `.env.local`
- **Autenticação**: Validação de funcionários via base de dados Sankhya
- **Controle de acesso**: Apenas funcionários cadastrados podem fazer pedidos
- **Limite de gastos**: Controle automático de R$ 200,00 mensais por funcionário
- **HTTPS**: Use sempre HTTPS em produção
- **Validação**: Dados validados no frontend e backend

## Deploy

Para fazer deploy em produção:

1. Configure as variáveis de ambiente no seu provedor de hosting
2. Execute o build: `npm run build`
3. Inicie a aplicação: `npm start`
